import { Box, Button, Grid2 } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import React, { useEffect, useMemo } from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import EmployeeCompensation, {
  formatCurrency,
} from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayrollComponentV2, PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { Component } from "./Compensation";

type Props = {
  form: any;
  isCurrentCompensation?: boolean;
  templates: PayrollTemplateV2[];
  isReadOnly?: boolean;
  preview: any;
  setPreview: (preview: any) => void;
};

const CompensationItem: React.FC<Props> = ({
  form,
  isCurrentCompensation,
  templates,
  isReadOnly,
  preview,
  setPreview,
}) => {
  const compensationDetails = useStore(
    form.store,
    (state: any) => state.values[isCurrentCompensation ? "current_compensation" : "next_compensation"],
  );

  const currency =
    compensationDetails?.components &&
    compensationDetails?.components?.reduce((__, curr: Component) => {
      return curr?.compensation_component?.currency;
    }, "");

  if (isReadOnly) {
    return (
      <EmployeeCompensation
        compensation={compensationDetails?.components}
        currency={currency}
        ctc={compensationDetails?.ctc}
      />
    );
  }
  const template = useMemo(() => {
    const template = templates?.find((template) => template.name === compensationDetails?.name);
    return template;
  }, [templates, compensationDetails]);
  const groupedCompensationDetails = useMemo(() => {
    const compensationDetailsByCompensationType = template?.components?.reduce(
      (acc, item) => {
        if (!acc[item?.compensation_component?.component_type]) {
          acc[item?.compensation_component?.component_type] = [item];
          return acc;
        }
        acc[item?.compensation_component?.component_type].push(item);
        return acc;
      },
      {} as Record<string, any[]>,
    );
    const results: any[] = [];
    if (!compensationDetailsByCompensationType) return [];
    Object.entries(compensationDetailsByCompensationType).forEach(([compensationType, compensationDetails]) => {
      results.push({
        name: `${compensationType}s`,
        isSection: true,
        component_type: compensationType,
        monthly: "",
        annually: "",
        description: "",
      });
      compensationDetails.forEach((compensationDetail) => {
        results.push({
          ...compensationDetail,
          isSection: false,
          monthly: compensationDetail?.amount,
          annually: compensationDetail?.amount * 12,
        });
      });
    });
    return results;
  }, [compensationDetails]);

  useEffect(() => {
    const key = isCurrentCompensation ? "current_compensation" : "next_compensation";
    if (compensationDetails?.components?.length === 0) {
      template?.components?.forEach((component: any) => {
        form.pushFieldValue(`${key}.components`, {
          ...component,
          amount: 0,
        });
      });
    }
  }, [template]);
  console.log({ compensationDetails, template, isCurrentCompensation: isCurrentCompensation ? "Yes" : "No" });

  const getComponentByCalculationType = (row: Component) => {
    const indexOfComponent = compensationDetails?.components?.findIndex(
      (eachComponent: any) => eachComponent.compensation_component.name === row.compensation_component.name,
    );
    const key = `${isCurrentCompensation ? "current_compensation" : "next_compensation"}.components[${indexOfComponent}]`;
    switch (row?.compensation_component?.calculation_type) {
      case "Flat":
        return (
          <form.AppField name={`${key}.compensation_component.formula.value`}>
            {(field: any) => (
              <field.EffiCurrency label="" currency={row?.compensation_component.currency} endHelperText="Annual" />
            )}
          </form.AppField>
        );
      case "Percentage":
        return (
          <form.AppField name={`${key}.compensation_component.formula.value`}>
            {(field: any) => (
              <field.EffiPercentageField
                label=""
                endHelperText={`of ${row?.compensation_component?.formula?.display_name}`}
              />
            )}
          </form.AppField>
        );
      case "Formula":
        return (
          <form.AppField name={`${key}.compensation_component.formula.value`}>
            {(field: any) => <field.EffiCurrency currency={row?.compensation_component?.currency} label="" />}
          </form.AppField>
        );
      default:
        return row?.compensation_component?.calculation_type;
    }
  };

  const onComputeClick = async () => {
    try {
      const resp = await payrollService.computePreview(
        compensationDetails?.components?.map((eachComponent: PayrollComponentV2) => ({
          ...eachComponent?.compensation_component,
        })),
        compensationDetails?.ctc,
      );
      setPreview((prev: any) => ({
        ...prev,
        [isCurrentCompensation ? "current_compensation" : "next_compensation"]: resp?.components,
      }));
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  };

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <Grid2 container spacing={2} alignItems="flex-end">
        <Grid2 size={6}>
          <form.AppField name={isCurrentCompensation ? "current_compensation.ctc" : "next_compensation.ctc"}>
            {(field: any) => <field.EffiCurrency label="CTC" currency={currency} endHelperText="Anually" />}
          </form.AppField>
        </Grid2>
        <Grid2 size={3} alignItems="flex-end">
          <Box>
            <Button size="small" onClick={onComputeClick}>
              Compute
            </Button>
          </Box>
        </Grid2>
      </Grid2>
      <DataTable
        columns={[
          {
            accessorKey: "compensation_component.name",
            header: "Component",
            size: 300,
          },
          {
            header: "Component Type",
            size: 250,
            Cell: ({ row }) => {
              if (row?.original?.isSection) return null;
              return getComponentByCalculationType(row?.original);
            },
          },
          {
            header: "Monthly Amount",
            size: 250,
            Cell: ({ row }) => {
              const value = preview?.[row.original?.compensation_component?.name];

              if (row?.original?.isSection) {
                return null;
              }

              if (!value) {
                return 0;
              }
              return formatCurrency(value / 12, row?.original?.compensation_component?.currency as string);
            },
          },
          {
            header: "Annual Amount",
            size: 250,
            Cell: ({ row }) => {
              const value = preview?.[row.original?.compensation_component?.name];

              if (row?.original?.isSection) {
                return null;
              }

              if (!value) {
                return 0;
              }
              return formatCurrency(value, row?.original?.compensation_component?.currency as string);
            },
          },
        ]}
        data={groupedCompensationDetails || []}
        muiTableBodyRowProps={({ row }) => ({
          sx: {
            backgroundColor: row.original.isSection ? "#F8FFFE" : "inherit",
            ...(row.original.isSection && {
              borderStyle: "dashed",
            }),
          },
        })}
      />
    </Box>
  );
};

export default CompensationItem;
