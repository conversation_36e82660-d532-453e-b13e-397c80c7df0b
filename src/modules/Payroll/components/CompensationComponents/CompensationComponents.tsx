import { Box, Button } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import { CompensationComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import ViewBenefits from "./Benefits/ViewBenefits";
import ViewDeductions from "./Deductions/ViewDeductions";
import ViewEarnings from "./Earnings/ViewEarnings";
import AddEditEarnings from "./Earnings/AddEditEarnings";
import AddEditDeductions from "./Deductions/AddEditDeductions";
import AddEditBenefits from "./Benefits/AddEditBenefits";

export const CompensationComponentContext = React.createContext({
  refetch: () => { },
  isLoading: false,
});

const compensationMaps = [
  {
    title: "Add Earnings",
    Component: AddEditEarnings,
  },
  {
    title: "Add Deductions",
    Component: AddEditDeductions,
  },
  {
    title: "Add Benefits",
    Component: AddEditBenefits,
  },
]

const categoriseCompensationComponents = (compensationComponent: CompensationComponent[]) => {
  return compensationComponent.reduce(
    (acc, item) => {
      if (!acc[item.component_type]) {
        acc[item.component_type] = [];
      }
      acc[item.component_type].push(item);
      return acc;
    },
    {} as Record<string, CompensationComponent[]>,
  );
};

const CompensationComponents = () => {
  const [activeTab, setActiveTab] = React.useState(0);
  const selectedTab = useMemo(() => compensationMaps[activeTab], [activeTab]);
  const [isAddModalOpen, setIsAddModalOpen] = React.useState(false);
  const {
    data: compensationComponents,
    refetch,
    isLoading,
  } = useQuery(["get-compensation-components"], async () => {
    const compensationComponents = await payrollService.getAllCompensationComponents("India");
    return categoriseCompensationComponents(compensationComponents as CompensationComponent[]) as Record<
      string,
      CompensationComponent[]
    >;
  });

  const handleChange = (value: number) => {
    setActiveTab(value);
  }

  const handleAddCompensationTemplate = (_activeTab: number) => {
    setIsAddModalOpen(true);
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Compensation Components" actions={<Button sx={{ width: 200 }} variant="contained" onClick={() => handleAddCompensationTemplate(activeTab)}>{compensationMaps[activeTab]?.title}</Button>} />
      <CompensationComponentContext.Provider value={{ refetch, isLoading }}>
        <TabsView
          handleTabChange={handleChange}
          tabs={[
            {
              id: 0,
              label: "Earning",
              component: <ViewEarnings earnings={compensationComponents?.Earning || []} />,
            },
            {
              id: 1,
              label: "Deductions",
              component: <ViewDeductions deductions={compensationComponents?.Deduction || []} />,
            },
            {
              id: 1,
              label: "Benefits",
              component: <ViewBenefits benefits={compensationComponents?.Benefit || []} />,
            },
          ]}
        />
      </CompensationComponentContext.Provider>
      <selectedTab.Component isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title={selectedTab.title} />
    </Box>
  );
};

export default CompensationComponents;
