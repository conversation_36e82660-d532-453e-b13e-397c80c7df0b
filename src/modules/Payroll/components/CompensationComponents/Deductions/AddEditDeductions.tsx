import React from "react";
import Modal from "src/modules/Common/Modal/Modal";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
}

const AddEditDeductions: React.FC<Props> = ({
  isOpen,
  onClose,
  title,
}) => {
  return (
      <Modal isOpen={isOpen} onClose={onClose} showBackButton title={title}>
        AddEditDeductions
      </Modal>
  );
};

export default AddEditDeductions;
