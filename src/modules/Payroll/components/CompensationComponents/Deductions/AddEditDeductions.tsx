import { Box, Button, Grid2, Paper } from "@mui/material";
import React from "react";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { z } from "zod";

const deductionsSchema = z.object({
  name: z.string().nonempty({
    message: "Deduction Name is required",
  }),
  frequency: z.enum(["OneTime", "Recurring"], {
    errorMap: () => ({ message: "Deduction Frequency is required" }),
  }),
});

type DeductionFormData = z.infer<typeof deductionsSchema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  onSubmit?: (data: DeductionFormData) => void;
}

const AddEditDeductions: React.FC<Props> = ({
  isOpen,
  onClose,
  title,
  onSubmit,
}) => {
  const form = useAppForm({
    defaultValues: {
      name: "",
      frequency: "OneTime",
    },
    validators: {
      onSubmit: deductionsSchema,
    },
  });

  const handleFormSubmit = (values: DeductionFormData) => {
    onSubmit?.(values);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      showBackButton
      title={title}
      actions={(
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
          {([canSubmit, isSubmitting, isPristine]) => {
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button variant="outlined" onClick={() => onClose()}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={!canSubmit || isPristine || isSubmitting}
                  onClick={() => form.handleSubmit(handleFormSubmit)}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      )}
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="name">
            {(field: any) => <field.EffiTextField label="Deduction Name" required placeholder="Enter deduction name" />}
          </form.AppField>
        </Grid2>
        <Grid2 size={12} component={Paper} elevation={2} p={2}>
          <form.AppField name="frequency">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Frequency of Deduction"
                layout="vertical"
                required
                options={[
                  { label: "One time deduction", value: "OneTime" },
                  { label: "Recurring deduction for subsequent Payrolls", value: "Recurring" },
                ]}
              />
            )}
          </form.AppField>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditDeductions;
