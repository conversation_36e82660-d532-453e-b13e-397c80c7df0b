import { Box, Button, Grid2, Paper } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import React from "react";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { z } from "zod";

const earningsSchema = z.object({
  name: z.string().nonempty({
    message: "Name is required",
  }),
  calculationType: z.enum(["Flat", "Percentage"]),
  amount: z.number().gt(0, {
    message: "Amount should be greater than 0",
  }),
  payType: z.enum(["Fixed", "Variable"]),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
}

const AddEditEarnings: React.FC<Props> = ({
  isOpen,
  onClose,
  title,
}) => {
  const form = useAppForm({
    defaultValues: {
      name: "",
      calculationType: "Flat",
      amount: 0,
      payType: "Fixed",
    },
    validators: {
      onSubmit: earningsSchema,
    },
  });

  const calculationType = useStore(form.store, (state) => state.values.calculationType);
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      showBackButton
      title={title}
      actions={(
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
          {([canSubmit, isSubmitting, isPristine]) => {
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button variant="outlined" onClick={() => onClose()}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={!canSubmit || isPristine || isSubmitting}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      )}
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="name">
            {(field: any) => <field.EffiTextField label="Earning Name" required />}
          </form.AppField>
        </Grid2>
        <Grid2 size={6} gap={1}>
          <Box component={Paper} elevation={2} p={2} height={200}>
            <form.AppField name="calculationType">
              {(field: any) => (
                <field.EffiRadioGroup
                  label="Calculation Type"
                  layout="horizontal"
                  required
                  options={[
                    { label: "Flat Amount", value: "Flat" },
                    { label: "Percentage of CTC", value: "Percentage" },
                  ]}
                />
              )}
            </form.AppField>
            <form.AppField name="amount">
              {(field: any) => {
                if (calculationType === "Percentage") {
                  return <field.EffiPercentageField label="Amount" required endHelperText="of CTC" placeholder="Enter percentage" />;
                }
                return <field.EffiCurrency label="Amount" required currency="INR" placeholder="Enter amount" />
              }}
            </form.AppField>
          </Box>
        </Grid2>
        <Grid2 size={6} gap={1}>
          <Box component={Paper} elevation={2} p={2} height={200}>
            <form.AppField name="payType">
              {(field: any) => (
                <field.EffiRadioGroup
                  label="Pay Type"
                  layout="vertical"
                  required
                  options={[
                    { label: "Fixed Pay", value: "Fixed", subLabel: "(Fixed amount paid at the end of every month)" },
                    { label: "Variable Pay", value: "Variable", subLabel: "(Variable amount paid any payroll)" },
                  ]}
                />
              )}
            </form.AppField>
          </Box>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditEarnings;
